@echo off
echo ============================================
echo React Project Fix Script for Windows
echo ============================================

REM 移至專案目錄
cd /d "C:\Users\<USER>\ClaudeCode\pdf-viewer-system\frontend"

echo.
echo [1/6] Cleaning up node_modules in all locations...
REM 清理當前目錄的 node_modules
if exist node_modules (
    echo Removing frontend\node_modules...
    rmdir /s /q node_modules
)

REM 清理使用者目錄下的 node_modules (錯誤位置)
if exist "C:\Users\<USER>\node_modules" (
    echo Removing C:\Users\<USER>\node_modules...
    rmdir /s /q "C:\Users\<USER>\node_modules"
)

REM 清理父目錄的 node_modules
if exist "..\node_modules" (
    echo Removing parent directory node_modules...
    rmdir /s /q "..\node_modules"
)

echo.
echo [2/6] Removing package-lock.json files...
if exist package-lock.json del /f package-lock.json
if exist ..\package-lock.json del /f ..\package-lock.json
if exist "C:\Users\<USER>\package-lock.json" del /f "C:\Users\<USER>\package-lock.json"

echo.
echo [3/6] Clearing npm cache...
call npm cache clean --force

echo.
echo [4/6] Clearing npm config prefix...
call npm config delete prefix

echo.
echo [5/6] Installing dependencies with legacy peer deps...
call npm install --legacy-peer-deps

echo.
echo [6/6] Verifying installation...
if exist node_modules\react-scripts (
    echo SUCCESS: react-scripts installed correctly!
    echo.
    echo You can now run: npm start
) else (
    echo ERROR: react-scripts not found. Please check errors above.
)

echo.
echo ============================================
echo Fix process completed!
echo ============================================
pause