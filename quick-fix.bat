@echo off
echo Quick Fix - Using compatible React 18 version
echo ============================================

REM 備份原始 package.json
copy package.json package.json.original

REM 使用相容版本
copy package-compatible.json package.json

REM 清理並重新安裝
if exist node_modules rmdir /s /q node_modules
if exist package-lock.json del /f package-lock.json

echo Installing with React 18 (compatible version)...
call npm install

echo.
echo Installation complete! Starting the app...
call npm start