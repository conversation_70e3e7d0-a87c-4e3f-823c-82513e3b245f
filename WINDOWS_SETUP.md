# Windows 開發環境設定指南

## 已完成的修復

1. **Git 換行符號處理**
   - 建立 `.gitattributes` 檔案確保跨平台換行符號一致性
   - 所有程式碼檔案統一使用 LF 換行符號
   - Windows 批次檔使用 CRLF

2. **環境變數設定**
   - 建立 `.env` 檔案管理環境變數
   - 新增 Windows 專用的 npm scripts (start:dev, start:prod, build:prod)

3. **Git 設定**
   執行以下命令設定 Git：
   ```bash
   git config --global core.autocrlf true
   git config --global core.eol lf
   ```

## 使用說明

### 開發環境啟動
```bash
# 安裝相依套件
npm install --legacy-peer-deps

# 開發模式
npm run start:dev

# 或使用預設 start（會讀取 .env 檔案）
npm start
```

### 建置專案
```bash
# 生產環境建置
npm run build:prod

# 或使用預設 build
npm run build
```

### 環境變數
專案使用 `.env` 檔案管理環境變數：
- `REACT_APP_API_URL`: API 伺服器位址
- `REACT_APP_ENV`: 環境設定 (development/production)

### 注意事項

1. **Node.js 版本**
   - 建議使用 Node.js v20 或更高版本
   - 目前某些套件需要 Node.js v20+

2. **IDE 設定**
   - VS Code: 安裝 EditorConfig 擴充套件
   - 確保編輯器使用 LF 換行符號

3. **Docker**
   - 如需使用 Docker，請安裝 Docker Desktop for Windows
   - 確保開啟 WSL2 支援

## 疑難排解

### npm install 錯誤
如遇到相依性衝突，使用：
```bash
npm install --legacy-peer-deps
```

### 換行符號問題
如果看到檔案全部被標記為修改：
```bash
# 重置換行符號
git add --renormalize .
git commit -m "Normalize line endings"
```

### 環境變數無效
確保：
1. `.env` 檔案在專案根目錄
2. 變數名稱以 `REACT_APP_` 開頭
3. 重新啟動開發伺服器