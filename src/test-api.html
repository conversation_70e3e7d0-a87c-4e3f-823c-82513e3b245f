<!DOCTYPE html>
<html>
<head>
    <title>Test API</title>
</head>
<body>
    <h1>Test Tree Structure API</h1>
    <pre id="result"></pre>
    
    <script>
        // 需要替換為實際的 token
        const token = localStorage.getItem('token') || 'YOUR_TOKEN_HERE';
        
        fetch('http://localhost:8000/api/documents/tree_structure/', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('result').textContent = JSON.stringify(data, null, 2);
            console.log('Tree structure:', data);
        })
        .catch(error => {
            document.getElementById('result').textContent = 'Error: ' + error.message;
            console.error('Error:', error);
        });
    </script>
</body>
</html>