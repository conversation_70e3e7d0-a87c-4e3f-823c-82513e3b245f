import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import api from '../config/api';
import { Card, CardContent } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { FileText, ArrowLeft, Star } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

interface FavoriteDocument {
  id: number;
  document: {
    id: number;
    title: string;
    category: string;
    subcategory: string;
    chapter: string;
  };
  created_at: string;
}

const Favorites: React.FC = () => {
  const [favorites, setFavorites] = useState<FavoriteDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();
  const { user } = useAuth();

  useEffect(() => {
    fetchFavorites();
  }, []);

  const fetchFavorites = async () => {
    setIsLoading(true);
    try {
      const response = await api.get('/favorites/');
      setFavorites(response.data);
    } catch (error) {
      console.error('Failed to fetch favorites:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDocumentClick = (documentId: number) => {
    navigate(`/document/${documentId}`);
  };

  const removeFavorite = async (documentId: number) => {
    try {
      await api.post(`/documents/${documentId}/toggle_favorite/`);
      // 重新獲取收藏列表
      fetchFavorites();
    } catch (error) {
      console.error('Failed to remove favorite:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回文檔列表
              </Button>
              <h1 className="text-xl font-semibold">我的收藏</h1>
            </div>
            <span className="text-sm text-gray-600">歡迎, {user?.username}</span>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {isLoading ? (
          <div className="text-center py-8">載入中...</div>
        ) : favorites.length === 0 ? (
          <div className="text-center py-16">
            <Star className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">尚未收藏任何文檔</p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => navigate('/')}
            >
              瀏覽文檔
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {favorites.map((fav) => (
              <Card
                key={fav.id}
                className="cursor-pointer hover:shadow-lg transition-shadow"
              >
                <CardContent className="p-4">
                  <div
                    className="flex items-start space-x-3"
                    onClick={() => handleDocumentClick(fav.document.id)}
                  >
                    <FileText className="h-5 w-5 text-gray-400 mt-1" />
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">{fav.document.title}</h3>
                      <p className="text-sm text-gray-500 mt-1">
                        {fav.document.category} 
                        {fav.document.subcategory && ` > ${fav.document.subcategory}`}
                        {fav.document.chapter && ` > ${fav.document.chapter}`}
                      </p>
                      <p className="text-xs text-gray-400 mt-2">
                        收藏於 {new Date(fav.created_at).toLocaleDateString('zh-TW')}
                      </p>
                    </div>
                  </div>
                  <div className="mt-3 flex justify-end">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeFavorite(fav.document.id);
                      }}
                    >
                      <Star className="h-4 w-4 mr-1 fill-current" />
                      取消收藏
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Favorites;