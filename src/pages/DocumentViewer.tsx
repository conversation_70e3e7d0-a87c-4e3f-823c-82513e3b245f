import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Document, Page, pdfjs } from 'react-pdf';
import api from '../config/api';
import { Button } from '../components/ui/button';
import { ChevronLeft, ChevronRight, Download, Star, Menu, X, Home, ChevronsLeft, ChevronsRight, ArrowUp, Keyboard, Search } from 'lucide-react';
import DocumentTree from '../components/DocumentTree';
import PDFDebugger from '../components/PDFDebugger';
import 'react-pdf/dist/Page/AnnotationLayer.css';
import 'react-pdf/dist/Page/TextLayer.css';

// 設定 PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js';

interface PDFDocument {
  id: number;
  title: string;
  file_path: string;
  category: string;
  subcategory: string;
  chapter: string;
}

interface RelatedDocument {
  id: number;
  title: string;
  category: string;
  subcategory: string;
  chapter: string;
}

const DocumentViewer: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [document, setDocument] = useState<PDFDocument | null>(null);
  const [pdfUrl, setPdfUrl] = useState<any>(null);
  const [numPages, setNumPages] = useState<number>(0);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [scale, setScale] = useState<number>(1.5);
  const [isLoading, setIsLoading] = useState(true);
  // const [relatedDocuments, setRelatedDocuments] = useState<RelatedDocument[]>([]);
  const [isFavorite, setIsFavorite] = useState(false);
  const [showSidebar, setShowSidebar] = useState(true);
  const [documentTree, setDocumentTree] = useState<any>(null);
  const [showDebugger, setShowDebugger] = useState(false);
  const [pageInputValue, setPageInputValue] = useState<string>('');
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [showKeyboardShortcuts, setShowKeyboardShortcuts] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [searchResults, setSearchResults] = useState<{page: number, text: string}[]>([]);
  const [currentSearchIndex, setCurrentSearchIndex] = useState(0);

  useEffect(() => {
    if (id) {
      fetchDocument();
      checkFavoriteStatus();
      fetchDocumentTree();
      // 切換文章時重置頁碼到第一頁
      setPageNumber(1);
      setPageInputValue('');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  // 鍵盤快捷鍵
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 如果正在輸入頁碼，不處理快捷鍵
      if (window.document.activeElement?.tagName === 'INPUT') return;

      switch (e.key) {
        case 'ArrowLeft':
        case 'ArrowUp':
          e.preventDefault();
          changePage(-1);
          break;
        case 'ArrowRight':
        case 'ArrowDown':
          e.preventDefault();
          changePage(1);
          break;
        case 'PageUp':
          e.preventDefault();
          changePage(-5);
          break;
        case 'PageDown':
          e.preventDefault();
          changePage(5);
          break;
        case 'Home':
          e.preventDefault();
          setPageNumber(1);
          break;
        case 'End':
          e.preventDefault();
          setPageNumber(numPages);
          break;
        case '+':
        case '=':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            setScale(prev => Math.min(prev + 0.2, 2));
          }
          break;
        case '-':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            setScale(prev => Math.max(prev - 0.2, 0.6));
          }
          break;
        case 'g':
        case 'G':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            const input = window.document.querySelector('input[type="number"]') as HTMLInputElement;
            input?.focus();
            input?.select();
          }
          break;
        case 'f':
        case 'F':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            setShowSearch(true);
          }
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pageNumber, numPages]);

  const fetchDocument = async () => {
    try {
      const response = await api.get(`/documents/${id}/`);
      setDocument(response.data);
      
      // 獲取PDF文件URL - 使用直接的 URL 而不是 blob
      const token = localStorage.getItem('access_token');
      const pdfUrl = `${api.defaults.baseURL}/documents/${id}/view/`;
      console.log('PDF URL:', pdfUrl);
      console.log('Token:', token ? 'exists' : 'missing');
      
      // 設置帶有認證 header 的 URL
      setPdfUrl({
        url: pdfUrl,
        httpHeaders: {
          'Authorization': `Bearer ${token}`
        },
        withCredentials: true
      });
    } catch (error) {
      console.error('Failed to fetch document:', error);
    } finally {
      setIsLoading(false);
    }
  };


  const checkFavoriteStatus = async () => {
    try {
      const response = await api.get(`/documents/${id}/is_favorite/`);
      setIsFavorite(response.data.is_favorite);
    } catch (error) {
      // 如果API不存在，默認為false
      setIsFavorite(false);
    }
  };

  const toggleFavorite = async () => {
    try {
      await api.post(`/documents/${id}/toggle_favorite/`);
      setIsFavorite(!isFavorite);
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
    }
  };

  const fetchDocumentTree = async () => {
    try {
      const response = await api.get('/documents/tree_structure/');
      setDocumentTree(response.data);
    } catch (error) {
      console.error('Failed to fetch document tree:', error);
    }
  };

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
  };

  const changePage = (offset: number) => {
    setPageNumber(prevPageNumber => {
      const newPageNumber = prevPageNumber + offset;
      if (newPageNumber > 0 && newPageNumber <= numPages) {
        // 更新後端進度 - 使用 catch 處理錯誤，避免影響用戶體驗
        api.post(`/documents/${id}/update_progress/`, { page_number: newPageNumber })
          .catch(error => {
            console.error('Failed to update progress:', error);
            // 靜默處理錯誤，不影響用戶繼續閱讀
          });
        return newPageNumber;
      }
      return prevPageNumber;
    });
  };

  const handleDownload = async () => {
    if (document) {
      const response = await api.get(`/documents/${id}/download/`, {
        responseType: 'blob'
      });
      const url = URL.createObjectURL(response.data);
      const a = window.document.createElement('a');
      a.href = url;
      a.download = `${document.title}.pdf`;
      a.click();
      URL.revokeObjectURL(url);
    }
  };

  if (isLoading) {
    return <div className="flex justify-center items-center min-h-screen">載入中...</div>;
  }

  if (!document || !pdfUrl) {
    return <div className="flex justify-center items-center min-h-screen">文檔未找到</div>;
  }

  return (
    <div className="min-h-screen bg-gray-100 flex">
      {/* 左側導航欄 */}
      <div className={`${showSidebar ? 'w-96' : 'w-0'} transition-all duration-300 bg-white border-r overflow-hidden flex-shrink-0`}>
        <div className="h-full overflow-y-auto">
          {/* 頂部導航 */}
          <div className="p-4 border-b bg-gray-50">
            <div className="flex items-center justify-between mb-3">
              <h2 className="text-lg font-semibold">文檔導航</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/')}
              >
                <Home className="h-4 w-4" />
              </Button>
            </div>
            
            {/* 當前文檔路徑 */}
            <div className="text-xs text-gray-600 space-y-1">
              <div className="flex items-center">
                <span className="font-medium">當前位置：</span>
              </div>
              <div className="pl-2 space-y-0.5">
                <div>{document.category}</div>
                {document.subcategory && (
                  <div className="pl-2">└─ {document.subcategory}</div>
                )}
                {document.chapter && (
                  <div className="pl-4">└─ {document.chapter}</div>
                )}
                <div className={`${document.chapter ? 'pl-6' : document.subcategory ? 'pl-4' : 'pl-2'} font-medium text-primary`}>
                  └─ {document.title}
                </div>
              </div>
            </div>
            
            <Button
              variant={isFavorite ? "default" : "outline"}
              size="sm"
              className="mt-3 w-full"
              onClick={toggleFavorite}
            >
              <Star className={`h-4 w-4 mr-2 ${isFavorite ? 'fill-current' : ''}`} />
              {isFavorite ? '已收藏' : '加入收藏'}
            </Button>
          </div>

          {/* 文檔樹狀結構 */}
          <div className="p-4">
            <h3 className="font-medium mb-3">所有文檔</h3>
            {documentTree && (
              <DocumentTree
                data={documentTree}
                currentDocumentId={parseInt(id!)}
                onDocumentSelect={(docId) => navigate(`/document/${docId}`)}
              />
            )}
          </div>
        </div>
      </div>

      {/* 主內容區 */}
      <div className="flex-1 flex flex-col">
        {/* 頂部工具欄 */}
        <header className="bg-white shadow-sm border-b sticky top-0 z-10">
          {/* 進度條 */}
          <div className="h-1 bg-gray-200">
            <div
              className="h-full bg-blue-500 transition-all duration-300"
              style={{ width: `${(pageNumber / numPages) * 100}%` }}
            />
          </div>
          
          <div className="px-4 h-16 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSidebar(!showSidebar)}
              >
                {showSidebar ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              </Button>
              <h1 className="text-lg font-semibold">{document.title}</h1>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* 頁面導航 */}
              <div className="flex items-center space-x-2">
                {/* 到第一頁 */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPageNumber(1)}
                  disabled={pageNumber <= 1}
                  title="第一頁"
                >
                  <ChevronsLeft className="h-4 w-4" />
                </Button>
                
                {/* 上一頁 */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => changePage(-1)}
                  disabled={pageNumber <= 1}
                  title="上一頁"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                
                {/* 頁數輸入 */}
                <div className="flex items-center">
                  <input
                    type="number"
                    min="1"
                    max={numPages}
                    value={pageInputValue || pageNumber}
                    onChange={(e) => setPageInputValue(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        const page = parseInt(pageInputValue);
                        if (page >= 1 && page <= numPages) {
                          setPageNumber(page);
                          setPageInputValue('');
                        }
                      }
                    }}
                    onBlur={() => {
                      const page = parseInt(pageInputValue);
                      if (page >= 1 && page <= numPages) {
                        setPageNumber(page);
                      }
                      setPageInputValue('');
                    }}
                    className="w-12 text-center text-sm border rounded px-1 py-0.5"
                  />
                  <span className="text-sm px-1">/ {numPages}</span>
                </div>
                
                {/* 下一頁 */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => changePage(1)}
                  disabled={pageNumber >= numPages}
                  title="下一頁"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
                
                {/* 到最後一頁 */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPageNumber(numPages)}
                  disabled={pageNumber >= numPages}
                  title="最後一頁"
                >
                  <ChevronsRight className="h-4 w-4" />
                </Button>
              </div>

              {/* 縮放控制 */}
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setScale(scale - 0.2)}
                  disabled={scale <= 0.6}
                >
                  -
                </Button>
                <span className="text-sm w-12 text-center">{Math.round(scale * 100)}%</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setScale(scale + 0.2)}
                  disabled={scale >= 2}
                >
                  +
                </Button>
              </div>

              {/* 下載按鈕 */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownload}
              >
                <Download className="h-4 w-4" />
              </Button>
              
              {/* 搜尋按鈕 */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSearch(!showSearch)}
                title="搜尋文字 (Ctrl/⌘ + F)"
              >
                <Search className="h-4 w-4" />
              </Button>
              
              {/* 診斷按鈕 */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowDebugger(!showDebugger)}
              >
                診斷
              </Button>
              
              {/* 快捷鍵提示按鈕 */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowKeyboardShortcuts(!showKeyboardShortcuts)}
                title="快捷鍵提示"
              >
                <Keyboard className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          {/* 快捷鍵提示框 */}
          {showKeyboardShortcuts && (
            <div className="absolute top-16 right-4 bg-white border rounded-lg shadow-lg p-4 z-20">
              <h3 className="font-semibold mb-2">鍵盤快捷鍵</h3>
              <div className="space-y-1 text-sm">
                <div>← → / ↑ ↓：上/下一頁</div>
                <div>Page Up/Down：快速翻頁（5頁）</div>
                <div>Home/End：第一頁/最後一頁</div>
                <div>Ctrl/⌘ + G：跳轉到頁數</div>
                <div>Ctrl/⌘ + F：搜尋文字</div>
                <div>Ctrl/⌘ + +/-：放大/縮小</div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="mt-2 w-full"
                onClick={() => setShowKeyboardShortcuts(false)}
              >
                關閉
              </Button>
            </div>
          )}
          
          {/* 搜尋框 */}
          {showSearch && (
            <div className="absolute top-16 left-1/2 transform -translate-x-1/2 bg-white border rounded-lg shadow-lg p-4 z-20 w-96">
              <div className="flex items-center space-x-2">
                <div className="flex-1 relative">
                  <input
                    type="text"
                    placeholder="搜尋 PDF 內容..."
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        // 搜尋邏輯將在這裡實現
                        console.log('搜尋:', searchText);
                      }
                      if (e.key === 'Escape') {
                        setShowSearch(false);
                        setSearchText('');
                      }
                    }}
                    className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    autoFocus
                  />
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setShowSearch(false);
                    setSearchText('');
                  }}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              {searchResults.length > 0 && (
                <div className="mt-2 text-sm text-gray-600">
                  找到 {searchResults.length} 個結果 
                  {searchResults.length > 0 && (
                    <span> (第 {currentSearchIndex + 1} 個)</span>
                  )}
                </div>
              )}
            </div>
          )}
        </header>

        {/* PDF 顯示區 */}
        <div className="flex-1 overflow-auto flex justify-center p-4 relative" id="pdf-container"
          onScroll={(e) => {
            const scrollTop = e.currentTarget.scrollTop;
            setShowScrollTop(scrollTop > 300);
          }}
        >
          <div className="border shadow-lg bg-white">
            <Document
              file={pdfUrl}
              onLoadSuccess={onDocumentLoadSuccess}
              onLoadError={(error) => {
                console.error('PDF 載入錯誤:', error);
                console.error('PDF URL:', pdfUrl);
                console.error('錯誤類型:', error.name);
                console.error('錯誤訊息:', error.message);
                if (error.stack) {
                  console.error('錯誤堆疊:', error.stack);
                }
              }}
              loading={<div className="p-8">載入 PDF 中...</div>}
              error={
                <div className="p-8 text-red-600">
                  <p>載入 PDF 失敗</p>
                  <p className="text-sm mt-2">請檢查瀏覽器控制台獲取更多資訊</p>
                  <p className="text-xs mt-2 text-gray-500">檔案：{document?.title}</p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-4"
                    onClick={() => setShowDebugger(!showDebugger)}
                  >
                    {showDebugger ? '隱藏' : '顯示'}診斷工具
                  </Button>
                </div>
              }
            >
              <Page
                pageNumber={pageNumber}
                scale={scale}
                renderTextLayer={true}
                renderAnnotationLayer={true}
              />
            </Document>
          </div>
          
          {/* 診斷工具 */}
          {showDebugger && pdfUrl && (
            <div className="mt-4 max-w-2xl">
              <PDFDebugger pdfUrl={pdfUrl} documentTitle={document?.title || ''} />
            </div>
          )}
          
          {/* 返回頂部按鈕 */}
          {showScrollTop && (
            <Button
              variant="default"
              size="sm"
              className="fixed bottom-20 right-8 rounded-full shadow-lg z-10"
              onClick={() => {
                const container = window.document.getElementById('pdf-container');
                container?.scrollTo({ top: 0, behavior: 'smooth' });
              }}
              title="返回頂部"
            >
              <ArrowUp className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* 底部狀態欄 */}
        <div className="bg-white border-t p-3">
          <div className="flex justify-between items-center max-w-6xl mx-auto">
            {/* 左側：閱讀進度 */}
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <span>進度：{Math.round((pageNumber / numPages) * 100)}%</span>
              <span>頁面：{pageNumber} / {numPages}</span>
              <span>縮放：{Math.round(scale * 100)}%</span>
            </div>
            
            {/* 中間：文檔路徑 */}
            <div className="flex-1 text-sm text-gray-600 text-center mx-4">
              <span>{document.category}</span>
              {document.subcategory && <span> / {document.subcategory}</span>}
              {document.chapter && <span> / {document.chapter}</span>}
              <span className="font-medium"> / {document.title}</span>
            </div>
            
            {/* 右側：狀態資訊 */}
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              {isFavorite && <span className="flex items-center"><Star className="h-3 w-3 mr-1 fill-current" />已收藏</span>}
              <span>{new Date().toLocaleTimeString()}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentViewer;