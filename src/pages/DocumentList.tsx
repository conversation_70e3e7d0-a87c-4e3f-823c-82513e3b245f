import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import api from '../config/api';
import { Input } from '../components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Search, FolderOpen, FileText, LogOut, ChevronRight, ChevronDown, Star, Folder } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import DocumentTree from '../components/DocumentTree';

interface Category {
  category: string;
  subcategories: string[];
  document_count: number;
}

interface Document {
  id: number;
  title: string;
  category: string;
  subcategory: string;
  chapter: string;
}

const DocumentList: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedSubcategory, setSelectedSubcategory] = useState('');
  const [selectedChapter, setSelectedChapter] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [expandedCategories, setExpandedCategories] = useState<string[]>([]);
  const [documentTree, setDocumentTree] = useState<any>(null);
  const [viewMode, setViewMode] = useState<'tree' | 'list'>('tree');
  const navigate = useNavigate();
  const { user, logout } = useAuth();

  useEffect(() => {
    fetchCategories();
    fetchDocuments();
    fetchDocumentTree();
  }, []);

  useEffect(() => {
    fetchDocuments();
  }, [searchTerm, selectedCategory, selectedSubcategory, selectedChapter]);

  const fetchCategories = async () => {
    try {
      const response = await api.get('/documents/categories/');
      setCategories(response.data);
    } catch (error) {
      console.error('Failed to fetch categories:', error);
    }
  };

  const fetchDocuments = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      if (selectedCategory) params.append('category', selectedCategory);
      if (selectedSubcategory) params.append('subcategory', selectedSubcategory);
      if (selectedChapter) params.append('chapter', selectedChapter);

      const response = await api.get(`/documents/?${params.toString()}`);
      setDocuments(response.data);
    } catch (error) {
      console.error('Failed to fetch documents:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchDocumentTree = async () => {
    try {
      const response = await api.get('/documents/tree_structure/');
      console.log('Tree structure response:', response.data);
      console.log('Response keys:', Object.keys(response.data));
      setDocumentTree(response.data);
    } catch (error: any) {
      console.error('Failed to fetch document tree:', error);
      console.error('Error details:', error.response?.data);
      if (error.response?.status === 401) {
        console.error('Authentication error - token may be expired');
      }
    }
  };

  const handleDocumentClick = (documentId: number) => {
    navigate(`/document/${documentId}`);
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const toggleCategory = (category: string) => {
    if (expandedCategories.includes(category)) {
      setExpandedCategories(expandedCategories.filter(c => c !== category));
    } else {
      setExpandedCategories([...expandedCategories, category]);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <h1 className="text-xl font-semibold">PDF 文檔瀏覽系統</h1>
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm" onClick={() => navigate('/favorites')}>
                <Star className="h-4 w-4 mr-2" />
                我的收藏
              </Button>
              <span className="text-sm text-gray-600">歡迎, {user?.username}</span>
              <Button variant="ghost" size="sm" onClick={handleLogout}>
                <LogOut className="h-4 w-4 mr-2" />
                登出
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* 側邊欄 */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">文檔導航</CardTitle>
              </CardHeader>
              <CardContent className="p-4">
                <div className="flex gap-2 mb-4">
                  <Button
                    variant={viewMode === 'tree' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('tree')}
                    className="flex-1"
                  >
                    <Folder className="h-4 w-4 mr-1" />
                    層次
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="flex-1"
                  >
                    <FileText className="h-4 w-4 mr-1" />
                    分類
                  </Button>
                </div>
                
                {viewMode === 'tree' ? (
                  documentTree && (
                    <DocumentTree
                      data={documentTree}
                      onDocumentSelect={(docId) => {
                        handleDocumentClick(docId);
                      }}
                    />
                  )
                ) : (
                  <div className="space-y-1">
                    <Button
                      variant={!selectedCategory ? "default" : "ghost"}
                      className="w-full justify-start"
                      onClick={() => {
                        setSelectedCategory('');
                        setSelectedSubcategory('');
                        setSelectedChapter('');
                      }}
                    >
                      <FolderOpen className="h-4 w-4 mr-2" />
                      全部文檔
                    </Button>
                    {categories.map((cat) => (
                      <div key={cat.category} className="space-y-1">
                        <div className={`rounded-md ${selectedCategory === cat.category ? 'bg-gray-100' : ''}`}>
                          <Button
                            variant="ghost"
                            className="w-full justify-between pr-2"
                            onClick={() => {
                              setSelectedCategory(cat.category);
                              setSelectedSubcategory('');
                              setSelectedChapter('');
                              if (cat.subcategories.length > 0) {
                                toggleCategory(cat.category);
                              }
                            }}
                          >
                            <div className="flex items-center">
                              <FolderOpen className="h-4 w-4 mr-2" />
                              <span className="font-medium">{cat.category}</span>
                              <span className="ml-2 text-sm text-gray-500">({cat.document_count})</span>
                            </div>
                            {cat.subcategories.length > 0 && (
                              expandedCategories.includes(cat.category) ? 
                                <ChevronDown className="h-4 w-4" /> : 
                                <ChevronRight className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        {expandedCategories.includes(cat.category) && cat.subcategories.length > 0 && (
                          <div className="ml-6 space-y-1">
                            {cat.subcategories.map((subcat) => (
                              <Button
                                key={subcat}
                                variant={selectedCategory === cat.category && selectedSubcategory === subcat ? "secondary" : "ghost"}
                                size="sm"
                                className="w-full justify-start text-sm"
                                onClick={() => {
                                  setSelectedCategory(cat.category);
                                  setSelectedSubcategory(subcat);
                                  setSelectedChapter('');
                                }}
                              >
                                <ChevronRight className="h-3 w-3 mr-1" />
                                <span className="truncate">{subcat}</span>
                              </Button>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* 主內容區 */}
          <div className="lg:col-span-3">
            <div className="mb-6">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  type="text"
                  placeholder="搜尋文檔..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {isLoading ? (
              <div className="text-center py-8">載入中...</div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {documents.map((doc) => (
                  <Card
                    key={doc.id}
                    className="cursor-pointer hover:shadow-lg transition-shadow"
                    onClick={() => handleDocumentClick(doc.id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start space-x-3">
                        <FileText className="h-5 w-5 text-gray-400 mt-1" />
                        <div className="flex-1">
                          <h3 className="font-medium text-gray-900">{doc.title}</h3>
                          <p className="text-sm text-gray-500 mt-1">
                            {doc.category}
                            {doc.subcategory && ` / ${doc.subcategory}`}
                            {doc.chapter && ` / ${doc.chapter}`}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {!isLoading && documents.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                沒有找到相關文檔
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentList;