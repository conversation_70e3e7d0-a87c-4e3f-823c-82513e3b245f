<!DOCTYPE html>
<html>
<head>
    <title>Test PDF Loading</title>
</head>
<body>
    <h1>Test PDF Loading</h1>
    <button onclick="testPDF()">Test Load PDF</button>
    <div id="result"></div>
    
    <script>
        async function testPDF() {
            const token = localStorage.getItem('token');
            const docId = 4276; // 使用已知存在的文檔 ID
            
            try {
                const response = await fetch(`http://localhost:8000/api/documents/${docId}/view/`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                if (response.ok) {
                    const blob = await response.blob();
                    console.log('Blob size:', blob.size);
                    console.log('Blob type:', blob.type);
                    
                    const url = URL.createObjectURL(blob);
                    console.log('Blob URL:', url);
                    
                    // 在新窗口中打開 PDF
                    window.open(url, '_blank');
                    
                    document.getElementById('result').innerHTML = `
                        <p>Success!</p>
                        <p>Blob size: ${blob.size}</p>
                        <p>Blob type: ${blob.type}</p>
                        <p>URL: ${url}</p>
                    `;
                } else {
                    const error = await response.text();
                    document.getElementById('result').innerHTML = `Error: ${response.status} - ${error}`;
                }
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('result').innerHTML = `Error: ${error.message}`;
            }
        }
    </script>
</body>
</html>