<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>常見問題與結語 - MVPN導入計畫</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft JhengHei', 'PingFang TC', sans-serif;
            background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
            min-height: 100vh;
            padding: 40px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 25px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            padding: 60px;
        }

        h1 {
            color: #2d3748;
            font-size: 3em;
            margin-bottom: 40px;
            display: flex;
            align-items: center;
            gap: 20px;
            border-bottom: 3px solid #8b5cf6;
            padding-bottom: 20px;
        }

        h2 {
            color: #6b21a8;
            font-size: 2em;
            margin: 40px 0 25px;
        }

        /* FAQ 手風琴樣式 */
        .faq-section {
            margin: 40px 0;
        }

        .faq-item {
            background: #faf5ff;
            border-radius: 15px;
            margin-bottom: 20px;
            overflow: hidden;
            border: 2px solid #e9d5ff;
            transition: all 0.3s;
        }

        .faq-item:hover {
            box-shadow: 0 10px 20px rgba(139, 92, 246, 0.1);
        }

        .faq-question {
            padding: 25px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
            transition: background 0.3s;
        }

        .faq-question:hover {
            background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
        }

        .question-text {
            font-size: 1.2em;
            font-weight: bold;
            color: #6b21a8;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .question-number {
            display: inline-block;
            width: 35px;
            height: 35px;
            background: #8b5cf6;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 35px;
            font-weight: bold;
        }

        .expand-icon {
            width: 30px;
            height: 30px;
            transition: transform 0.3s;
        }

        .faq-item.active .expand-icon {
            transform: rotate(180deg);
        }

        .faq-answer {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
            background: white;
        }

        .faq-item.active .faq-answer {
            max-height: 500px;
            transition: max-height 0.5s ease-in;
        }

        .answer-content {
            padding: 25px;
            color: #4a5568;
            line-height: 1.8;
            font-size: 1.1em;
        }

        .answer-box {
            background: #f0fdf4;
            border-left: 4px solid #10b981;
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
        }

        /* 重點摘要卡片 */
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 40px 0;
        }

        .summary-card {
            background: linear-gradient(135deg, #ddd6fe 0%, #c4b5fd 100%);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            transition: transform 0.3s;
        }

        .summary-card:hover {
            transform: translateY(-5px);
        }

        .summary-icon {
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
        }

        .summary-title {
            font-size: 1.5em;
            color: #6b21a8;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .summary-text {
            color: #4a5568;
            line-height: 1.6;
        }

        /* 聯絡資訊 */
        .contact-section {
            background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            text-align: center;
        }

        .contact-title {
            font-size: 2em;
            color: #92400e;
            margin-bottom: 30px;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .contact-item {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .contact-icon {
            width: 50px;
            height: 50px;
            background: #f59e0b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
        }

        .contact-label {
            font-weight: bold;
            color: #92400e;
            margin-bottom: 8px;
        }

        .contact-value {
            color: #6b7280;
            font-size: 1.1em;
        }

        /* 下一步行動 */
        .next-steps {
            background: linear-gradient(135deg, #a7f3d0 0%, #6ee7b7 100%);
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
        }

        .steps-timeline {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            position: relative;
        }

        .steps-timeline::before {
            content: '';
            position: absolute;
            top: 25px;
            left: 10%;
            right: 10%;
            height: 3px;
            background: #10b981;
            z-index: 0;
        }

        .step-item {
            flex: 1;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .step-circle {
            width: 50px;
            height: 50px;
            background: white;
            border: 3px solid #10b981;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-weight: bold;
            color: #10b981;
            font-size: 1.2em;
        }

        .step-title {
            font-weight: bold;
            color: #047857;
            margin-bottom: 8px;
        }

        .step-desc {
            color: #065f46;
            font-size: 0.95em;
        }

        /* 最終呼籲 */
        .final-cta {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 50px;
            text-align: center;
            margin-top: 40px;
            position: relative;
            overflow: hidden;
        }

        .final-cta::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }

        .cta-title {
            font-size: 3em;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .cta-subtitle {
            font-size: 1.5em;
            margin-bottom: 30px;
            position: relative;
            z-index: 1;
        }

        .cta-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            position: relative;
            z-index: 1;
        }

        .cta-btn {
            padding: 15px 40px;
            border-radius: 30px;
            font-size: 1.2em;
            font-weight: bold;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
        }

        .cta-btn-primary {
            background: white;
            color: #667eea;
        }

        .cta-btn-secondary {
            background: transparent;
            color: white;
            border: 2px solid white;
        }

        .cta-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        /* 參考資料 */
        .references {
            background: #f9fafb;
            border-radius: 15px;
            padding: 30px;
            margin: 40px 0;
        }

        .ref-title {
            font-size: 1.3em;
            color: #4a5568;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .ref-list {
            list-style: none;
            padding: 0;
        }

        .ref-item {
            padding: 10px 0;
            border-bottom: 1px solid #e5e7eb;
            color: #6b7280;
            display: flex;
            align-items: start;
            gap: 10px;
        }

        .ref-item:last-child {
            border-bottom: none;
        }

        .ref-icon {
            color: #8b5cf6;
            flex-shrink: 0;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>
        <svg width="60" height="60" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="#8b5cf6" stroke-width="2"/>
            <path d="M12 7V12M12 16H12.01" stroke="#8b5cf6" stroke-width="2" stroke-linecap="round"/>
        </svg>
        常見問題與結語
    </h1>

    <h2>❓ 常見問題解答</h2>
    <div class="faq-section">
        <div class="faq-item active">
            <div class="faq-question" onclick="toggleFAQ(this)">
                    <span class="question-text">
                        <span class="question-number">Q1</span>
                        500-600台機器真的2-3個月能完成嗎？
                    </span>
                <svg class="expand-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M19 9L12 16L5 9" stroke="#8b5cf6" stroke-width="2" stroke-linecap="round"/>
                </svg>
            </div>
            <div class="faq-answer">
                <div class="answer-content">
                    <div class="answer-box">
                        <strong>答案：按經銷商分批執行可行</strong>
                    </div>
                    <ul>
                        <li>• 每家經銷商獨立作業5-10天</li>
                        <li>• 8家依序執行，互不干擾</li>
                        <li>• 加上緩衝時間約2-3個月</li>
                        <li>• 已考慮測試驗證時間</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="faq-item">
            <div class="faq-question" onclick="toggleFAQ(this)">
                    <span class="question-text">
                        <span class="question-number">Q2</span>
                        會不會影響營業？
                    </span>
                <svg class="expand-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M19 9L12 16L5 9" stroke="#8b5cf6" stroke-width="2" stroke-linecap="round"/>
                </svg>
            </div>
            <div class="faq-answer">
                <div class="answer-content">
                    <div class="answer-box">
                        <strong>答案：影響很小</strong>
                    </div>
                    <ul>
                        <li>• 機台斷網仍可正常販售（離線模式）</li>
                        <li>• 只是暫時無法回傳資料</li>
                        <li>• 連網後資料會自動補傳</li>
                        <li>• 選擇淡季執行影響最小</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="faq-item">
            <div class="faq-question" onclick="toggleFAQ(this)">
                    <span class="question-text">
                        <span class="question-number">Q3</span>
                        簡易型機台改MVPN有什麼好處？
                    </span>
                <svg class="expand-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M19 9L12 16L5 9" stroke="#8b5cf6" stroke-width="2" stroke-linecap="round"/>
                </svg>
            </div>
            <div class="faq-answer">
                <div class="answer-content">
                    <div class="answer-box">
                        <strong>答案：主要是安全性提升</strong>
                    </div>
                    <ul>
                        <li>• 防止被當作攻擊跳板</li>
                        <li>• 保護資料傳輸安全</li>
                        <li>• IP固定便於管理識別</li>
                        <li>• 為未來升級做準備</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="faq-item">
            <div class="faq-question" onclick="toggleFAQ(this)">
                    <span class="question-text">
                        <span class="question-number">Q4</span>
                        進階型機台改善多少？
                    </span>
                <svg class="expand-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M19 9L12 16L5 9" stroke="#8b5cf6" stroke-width="2" stroke-linecap="round"/>
                </svg>
            </div>
            <div class="faq-answer">
                <div class="answer-content">
                    <div class="answer-box">
                        <strong>答案：顯著提升</strong>
                    </div>
                    <ul>
                        <li>• 遠端軟體連線從不穩定變成穩定</li>
                        <li>• 減少50%以上現場處理需求</li>
                        <li>• 問題解決效率提高60%</li>
                        <li>• 大幅降低維護成本</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="faq-item">
            <div class="faq-question" onclick="toggleFAQ(this)">
                    <span class="question-text">
                        <span class="question-number">Q5</span>
                        MVPN可以雙向連線嗎？
                    </span>
                <svg class="expand-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M19 9L12 16L5 9" stroke="#8b5cf6" stroke-width="2" stroke-linecap="round"/>
                </svg>
            </div>
            <div class="faq-answer">
                <div class="answer-content">
                    <div class="answer-box">
                        <strong>答案：需要額外方案</strong>
                    </div>
                    <ul>
                        <li>• MVPN本身是單向（機台到公司）</li>
                        <li>• 若需雙向，可加裝4G路由器</li>
                        <li>• 或在機房拉ELC專線</li>
                        <li>• 依實際需求決定，成本可控</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <h2>📋 重點摘要</h2>
    <div class="summary-cards">
        <div class="summary-card">
            <div class="summary-icon">
                <svg width="50" height="50" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L2 7V11C2 16.55 5.84 21.74 11 22.93C16.16 21.74 20 16.55 20 11V7L12 2Z" fill="#8b5cf6" opacity="0.3"/>
                    <path d="M9 12L11 14L15 10" stroke="#8b5cf6" stroke-width="2" stroke-linecap="round"/>
                </svg>
            </div>
            <div class="summary-title">安全升級</div>
            <div class="summary-text">從浮動IP升級到MVPN，大幅提升網路安全性，保護公司與客戶資料</div>
        </div>

        <div class="summary-card">
            <div class="summary-icon">
                <svg width="50" height="50" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="3" y="3" width="18" height="18" rx="2" stroke="#8b5cf6" stroke-width="2"/>
                    <path d="M3 9H21M9 9V21" stroke="#8b5cf6" stroke-width="2"/>
                </svg>
            </div>
            <div class="summary-title">管理效率</div>
            <div class="summary-text">固定IP讓每台機器可識別追蹤，管理效率提升60%以上</div>
        </div>

        <div class="summary-card">
            <div class="summary-icon">
                <svg width="50" height="50" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M13 7L18 12M18 12L13 17M18 12H6" stroke="#8b5cf6" stroke-width="2" stroke-linecap="round"/>
                    <circle cx="12" cy="12" r="10" stroke="#8b5cf6" stroke-width="2"/>
                </svg>
            </div>
            <div class="summary-title">未來擴展</div>
            <div class="summary-text">為6000台規模做好準備，支援未來雲端化發展</div>
        </div>
    </div>

    <h2>🚀 下一步行動</h2>
    <div class="next-steps">
        <h3 style="text-align: center; color: #047857; margin-bottom: 10px;">建議執行步驟</h3>
        <div class="steps-timeline">
            <div class="step-item">
                <div class="step-circle">1</div>
                <div class="step-title">8月初</div>
                <div class="step-desc">決策會議</div>
            </div>
            <div class="step-item">
                <div class="step-circle">2</div>
                <div class="step-title">8-9月</div>
                <div class="step-desc">測試驗證</div>
            </div>
            <div class="step-item">
                <div class="step-circle">3</div>
                <div class="step-title">10月</div>
                <div class="step-desc">準備部署</div>
            </div>
            <div class="step-item">
                <div class="step-circle">4</div>
                <div class="step-title">11-1月</div>
                <div class="step-desc">全面執行</div>
            </div>
        </div>
    </div>



    <div class="references">
        <div class="ref-title">📚 參考資料</div>
        <ul class="ref-list">
            <li class="ref-item">
                <svg class="ref-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
                遠傳電信MVPN技術規格書（2025年版）
            </li>
            <li class="ref-item">
                <svg class="ref-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
                2025年全球IoT設備威脅報告 - Kaspersky Lab
            </li>
            <li class="ref-item">
                <svg class="ref-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
                台灣網路威脅趨勢分析 - 國家資通安全研究院
            </li>
            <li class="ref-item">
                <svg class="ref-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
                自販機產業數位轉型白皮書 - 經濟部工業局
            </li>
        </ul>
    </div>

    <div class="final-cta">
        <h1 class="cta-title">把握最佳時機</h1>
        <p class="cta-subtitle">
            500-600台是轉換的黃金期<br>
            讓我們一起建立安全的營運環境
        </p>
    </div>
</div>

<script>
    function toggleFAQ(element) {
        const faqItem = element.parentElement;
        const allItems = document.querySelectorAll('.faq-item');

        // 關閉其他項目
        allItems.forEach(item => {
            if (item !== faqItem) {
                item.classList.remove('active');
            }
        });

        // 切換當前項目
        faqItem.classList.toggle('active');
    }
</script>
</body>
</html>