import React, { useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import { Button } from './ui/button';

interface PDFDebuggerProps {
  pdfUrl: any;
  documentTitle: string;
}

const PDFDebugger: React.FC<PDFDebuggerProps> = ({ pdfUrl, documentTitle }) => {
  const [debugInfo, setDebugInfo] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [loadError, setLoadError] = useState<any>(null);

  const addDebugInfo = (info: string) => {
    setDebugInfo(prev => [...prev, `[${new Date().toISOString()}] ${info}`]);
  };

  const testDirectFetch = async () => {
    if (!pdfUrl || !pdfUrl.url) return;
    
    try {
      addDebugInfo('開始直接 fetch 測試...');
      const response = await fetch(pdfUrl.url, {
        headers: pdfUrl.httpHeaders || {},
        credentials: pdfUrl.withCredentials ? 'include' : 'same-origin'
      });
      
      addDebugInfo(`Fetch 狀態: ${response.status} ${response.statusText}`);
      addDebugInfo(`Content-Type: ${response.headers.get('content-type')}`);
      addDebugInfo(`Content-Length: ${response.headers.get('content-length')}`);
      
      if (!response.ok) {
        const text = await response.text();
        addDebugInfo(`錯誤內容: ${text.substring(0, 200)}...`);
      } else {
        const blob = await response.blob();
        addDebugInfo(`成功獲取 PDF, 大小: ${blob.size} bytes`);
      }
    } catch (error: any) {
      addDebugInfo(`Fetch 錯誤: ${error.message}`);
    }
  };

  return (
    <div className="bg-gray-100 p-4 rounded-lg">
      <h3 className="font-bold mb-2">PDF 載入診斷工具</h3>
      <div className="mb-4">
        <p className="text-sm"><strong>文件名稱:</strong> {documentTitle}</p>
        <p className="text-sm"><strong>PDF URL:</strong> {pdfUrl?.url || 'N/A'}</p>
        <p className="text-sm"><strong>PDF.js 版本:</strong> {pdfjs.version}</p>
        <p className="text-sm"><strong>Worker 路徑:</strong> {pdfjs.GlobalWorkerOptions.workerSrc}</p>
      </div>
      
      <div className="mb-4">
        <Button onClick={testDirectFetch} size="sm">
          測試直接 Fetch
        </Button>
      </div>

      <div className="mb-4">
        <h4 className="font-semibold mb-1">迷你 PDF 預覽:</h4>
        <div className="border bg-white p-2" style={{ height: '200px', overflow: 'hidden' }}>
          <Document
            file={pdfUrl}
            onLoadSuccess={(pdf) => {
              addDebugInfo(`PDF 載入成功! 頁數: ${pdf.numPages}`);
              setIsLoading(false);
            }}
            onLoadError={(error) => {
              addDebugInfo(`PDF 載入失敗!`);
              addDebugInfo(`錯誤類型: ${error.name}`);
              addDebugInfo(`錯誤訊息: ${error.message}`);
              if (error.stack) {
                addDebugInfo(`堆疊追蹤: ${error.stack.split('\n')[0]}`);
              }
              setLoadError(error);
              setIsLoading(false);
            }}
            loading={<div className="text-sm">載入中...</div>}
            error={<div className="text-sm text-red-600">載入失敗</div>}
          >
            <Page 
              pageNumber={1} 
              width={150}
              renderTextLayer={false}
              renderAnnotationLayer={false}
            />
          </Document>
        </div>
      </div>

      <div className="bg-gray-800 text-gray-200 p-2 rounded text-xs font-mono max-h-40 overflow-y-auto">
        <h4 className="font-semibold mb-1">診斷日誌:</h4>
        {debugInfo.map((info, index) => (
          <div key={index}>{info}</div>
        ))}
      </div>
    </div>
  );
};

export default PDFDebugger;