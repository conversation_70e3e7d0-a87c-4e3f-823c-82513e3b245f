import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from './ui/button';
import { ChevronRight, ChevronDown, FileText, FolderOpen, Folder, Search, X, RotateCcw } from 'lucide-react';

interface Document {
  id: number;
  title: string;
  order: number;
}

interface Chapter {
  name: string;
  documents: Document[];
}

interface Subcategory {
  name: string;
  chapters: { [key: string]: Chapter };
  documents: Document[];
}

interface Category {
  name: string;
  subcategories: { [key: string]: Subcategory };
  documents: Document[];
}

interface VehicleModel {
  name: string;
  categories: { [key: string]: Category };
  documents: Document[];
}

interface DocumentTreeProps {
  data: { [key: string]: VehicleModel };
  currentDocumentId?: number;
  onDocumentSelect?: (documentId: number) => void;
}

const DocumentTree: React.FC<DocumentTreeProps> = ({ data, currentDocumentId, onDocumentSelect }) => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredData, setFilteredData] = useState(data);
  
  // 從 localStorage 讀取展開狀態
  const [expandedVehicles, setExpandedVehicles] = useState<string[]>(() => {
    const saved = localStorage.getItem('expandedVehicles');
    return saved ? JSON.parse(saved) : [];
  });
  const [expandedCategories, setExpandedCategories] = useState<string[]>(() => {
    const saved = localStorage.getItem('expandedCategories');
    return saved ? JSON.parse(saved) : [];
  });
  const [expandedSubcategories, setExpandedSubcategories] = useState<string[]>(() => {
    const saved = localStorage.getItem('expandedSubcategories');
    return saved ? JSON.parse(saved) : [];
  });
  const [expandedChapters, setExpandedChapters] = useState<string[]>(() => {
    const saved = localStorage.getItem('expandedChapters');
    return saved ? JSON.parse(saved) : [];
  });

  // 當 data 改變時，更新 filteredData
  useEffect(() => {
    setFilteredData(data);
  }, [data]);

  // 保存展開狀態到 localStorage
  useEffect(() => {
    localStorage.setItem('expandedVehicles', JSON.stringify(expandedVehicles));
  }, [expandedVehicles]);

  useEffect(() => {
    localStorage.setItem('expandedCategories', JSON.stringify(expandedCategories));
  }, [expandedCategories]);

  useEffect(() => {
    localStorage.setItem('expandedSubcategories', JSON.stringify(expandedSubcategories));
  }, [expandedSubcategories]);

  useEffect(() => {
    localStorage.setItem('expandedChapters', JSON.stringify(expandedChapters));
  }, [expandedChapters]);

  // 搜尋過濾邏輯
  useEffect(() => {
    if (!searchQuery) {
      setFilteredData(data);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered: { [key: string]: VehicleModel } = {};

    Object.entries(data || {}).forEach(([vehicleKey, vehicle]) => {
      const filteredVehicle: VehicleModel = {
        name: vehicle.name,
        documents: vehicle.documents.filter(doc => 
          doc.title.toLowerCase().includes(query)
        ),
        categories: {}
      };

      // 過濾分類
      Object.entries(vehicle.categories).forEach(([catKey, category]) => {
        const filteredCategory: Category = {
          name: category.name,
          documents: category.documents.filter(doc => 
            doc.title.toLowerCase().includes(query)
          ),
          subcategories: {}
        };

        // 過濾子分類
        Object.entries(category.subcategories).forEach(([subKey, subcategory]) => {
          const filteredSubcategory: Subcategory = {
            name: subcategory.name,
            documents: subcategory.documents.filter(doc => 
              doc.title.toLowerCase().includes(query)
            ),
            chapters: {}
          };

          // 過濾章節
          Object.entries(subcategory.chapters).forEach(([chapKey, chapter]) => {
            const filteredDocuments = chapter.documents.filter(doc => 
              doc.title.toLowerCase().includes(query)
            );
            
            if (filteredDocuments.length > 0) {
              filteredSubcategory.chapters[chapKey] = {
                name: chapter.name,
                documents: filteredDocuments
              };
            }
          });

          // 如果子分類有文檔或章節，則包含它
          if (filteredSubcategory.documents.length > 0 || 
              Object.keys(filteredSubcategory.chapters).length > 0) {
            filteredCategory.subcategories[subKey] = filteredSubcategory;
          }
        });

        // 如果分類有文檔或子分類，則包含它
        if (filteredCategory.documents.length > 0 || 
            Object.keys(filteredCategory.subcategories).length > 0) {
          filteredVehicle.categories[catKey] = filteredCategory;
        }
      });

      // 如果車型有文檔或分類，則包含它
      if (filteredVehicle.documents.length > 0 || 
          Object.keys(filteredVehicle.categories).length > 0) {
        filtered[vehicleKey] = filteredVehicle;
      }
    });

    setFilteredData(filtered);
  }, [searchQuery, data]);

  const toggleVehicle = (vehicle: string) => {
    setExpandedVehicles(prev =>
      prev.includes(vehicle)
        ? prev.filter(v => v !== vehicle)
        : [...prev, vehicle]
    );
  };

  const toggleCategory = (category: string) => {
    setExpandedCategories(prev =>
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  };

  const toggleSubcategory = (subcategory: string) => {
    setExpandedSubcategories(prev =>
      prev.includes(subcategory)
        ? prev.filter(s => s !== subcategory)
        : [...prev, subcategory]
    );
  };

  const toggleChapter = (chapter: string) => {
    setExpandedChapters(prev =>
      prev.includes(chapter)
        ? prev.filter(c => c !== chapter)
        : [...prev, chapter]
    );
  };

  const handleDocumentClick = (documentId: number) => {
    if (onDocumentSelect) {
      onDocumentSelect(documentId);
    } else {
      navigate(`/document/${documentId}`);
    }
  };

  // 重置所有展開狀態
  const resetExpandedState = () => {
    setExpandedVehicles([]);
    setExpandedCategories([]);
    setExpandedSubcategories([]);
    setExpandedChapters([]);
    localStorage.removeItem('expandedVehicles');
    localStorage.removeItem('expandedCategories');
    localStorage.removeItem('expandedSubcategories');
    localStorage.removeItem('expandedChapters');
  };

  const renderDocuments = (documents: Document[], level: number = 0) => {
    return documents.map(doc => (
      <Button
        key={doc.id}
        variant={currentDocumentId === doc.id ? "secondary" : "ghost"}
        size="sm"
        className="w-full justify-start text-left py-2 h-auto"
        style={{ paddingLeft: `${16 + level * 16}px` }}
        onClick={() => handleDocumentClick(doc.id)}
      >
        <FileText className="h-3 w-3 mr-2 flex-shrink-0 mt-0.5" />
        <span className="text-xs break-all whitespace-normal leading-relaxed">{doc.title}</span>
      </Button>
    ));
  };

  // 計算文檔總數
  const countDocuments = (treeData: any): number => {
    if (!treeData) return 0;
    let count = 0;
    
    Object.values(treeData).forEach((vehicle: any) => {
      count += vehicle.documents.length;
      
      Object.values(vehicle.categories).forEach((category: any) => {
        count += category.documents.length;
        
        Object.values(category.subcategories).forEach((subcategory: any) => {
          count += subcategory.documents.length;
          
          Object.values(subcategory.chapters).forEach((chapter: any) => {
            count += chapter.documents.length;
          });
        });
      });
    });
    
    return count;
  };

  console.log('DocumentTree received data:', data);
  
  if (!data || Object.keys(data).length === 0) {
    return <div className="text-sm text-gray-500 p-2">沒有文檔</div>;
  }

  return (
    <div className="space-y-2">
      {/* 搜尋框和控制按鈕 */}
      <div className="space-y-2 mb-3">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="搜尋文檔..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-10 py-2 text-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSearchQuery('')}
              className="absolute right-1 top-1/2 transform -translate-y-1/2 p-1 h-auto"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
        {searchQuery && (
          <div className="text-xs text-gray-500">
            找到 {countDocuments(filteredData)} 個文檔
          </div>
        )}
        <Button
          variant="outline"
          size="sm"
          onClick={resetExpandedState}
          className="w-full flex items-center justify-center gap-2 text-xs"
        >
          <RotateCcw className="h-3 w-3" />
          重置導航列
        </Button>
      </div>

      {/* 文檔樹 */}
      {Object.entries(filteredData || {}).map(([vehicleKey, vehicle]) => (
        <div key={vehicleKey} className="space-y-1.5">
          <Button
            variant="ghost"
            size="sm"
            className="w-full justify-start font-bold py-2 h-auto bg-gray-100"
            onClick={() => toggleVehicle(vehicleKey)}
          >
            <div className="flex items-start w-full">
              {expandedVehicles.includes(vehicleKey) ? (
                <ChevronDown className="h-4 w-4 mr-2 flex-shrink-0 mt-0.5" />
              ) : (
                <ChevronRight className="h-4 w-4 mr-2 flex-shrink-0 mt-0.5" />
              )}
              <span className="break-all whitespace-normal leading-relaxed">車型: {vehicle.name}</span>
            </div>
          </Button>

          {expandedVehicles.includes(vehicleKey) && (
            <div className="ml-4 space-y-1">
              {/* 直接在車型下的文檔 */}
              {vehicle.documents.length > 0 && renderDocuments(vehicle.documents, 1)}

              {/* 分類 */}
              {Object.entries(vehicle.categories).map(([categoryKey, category]) => (
                <div key={categoryKey} className="space-y-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start font-medium py-2 h-auto"
                    onClick={() => toggleCategory(`${vehicleKey}-${categoryKey}`)}
                  >
                    <div className="flex items-start w-full">
                      {expandedCategories.includes(`${vehicleKey}-${categoryKey}`) ? (
                        <ChevronDown className="h-4 w-4 mr-2 flex-shrink-0 mt-0.5" />
                      ) : (
                        <ChevronRight className="h-4 w-4 mr-2 flex-shrink-0 mt-0.5" />
                      )}
                      {expandedCategories.includes(`${vehicleKey}-${categoryKey}`) ? (
                        <FolderOpen className="h-4 w-4 mr-2 flex-shrink-0 mt-0.5" />
                      ) : (
                        <Folder className="h-4 w-4 mr-2 flex-shrink-0 mt-0.5" />
                      )}
                      <span className="break-all whitespace-normal leading-relaxed">{category.name}</span>
                    </div>
                  </Button>

                  {expandedCategories.includes(`${vehicleKey}-${categoryKey}`) && (
                    <div className="ml-4 space-y-1">
                      {/* 直接在分類下的文檔 */}
                      {category.documents.length > 0 && renderDocuments(category.documents, 2)}

                      {/* 子分類 */}
                      {Object.entries(category.subcategories).map(([subcategoryKey, subcategory]) => (
                        <div key={subcategoryKey} className="space-y-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="w-full justify-start text-sm py-2 h-auto"
                            onClick={() => toggleSubcategory(`${vehicleKey}-${categoryKey}-${subcategoryKey}`)}
                          >
                            <div className="flex items-start w-full">
                              {expandedSubcategories.includes(`${vehicleKey}-${categoryKey}-${subcategoryKey}`) ? (
                                <ChevronDown className="h-3 w-3 mr-2 flex-shrink-0 mt-0.5" />
                              ) : (
                                <ChevronRight className="h-3 w-3 mr-2 flex-shrink-0 mt-0.5" />
                              )}
                              {expandedSubcategories.includes(`${vehicleKey}-${categoryKey}-${subcategoryKey}`) ? (
                                <FolderOpen className="h-3 w-3 mr-2 flex-shrink-0 mt-0.5" />
                              ) : (
                                <Folder className="h-3 w-3 mr-2 flex-shrink-0 mt-0.5" />
                              )}
                              <span className="break-all whitespace-normal leading-relaxed">{subcategory.name}</span>
                            </div>
                          </Button>

                          {expandedSubcategories.includes(`${vehicleKey}-${categoryKey}-${subcategoryKey}`) && (
                            <div className="ml-4 space-y-1">
                              {/* 直接在子分類下的文檔 */}
                              {subcategory.documents.length > 0 && renderDocuments(subcategory.documents, 3)}

                              {/* 章節 */}
                              {Object.entries(subcategory.chapters).map(([chapterKey, chapter]) => (
                                <div key={chapterKey} className="space-y-1">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="w-full justify-start text-xs py-2 h-auto"
                                    onClick={() => toggleChapter(`${vehicleKey}-${categoryKey}-${subcategoryKey}-${chapterKey}`)}
                                  >
                                    <div className="flex items-start w-full">
                                      {expandedChapters.includes(`${vehicleKey}-${categoryKey}-${subcategoryKey}-${chapterKey}`) ? (
                                        <ChevronDown className="h-3 w-3 mr-2 flex-shrink-0 mt-0.5" />
                                      ) : (
                                        <ChevronRight className="h-3 w-3 mr-2 flex-shrink-0 mt-0.5" />
                                      )}
                                      {expandedChapters.includes(`${vehicleKey}-${categoryKey}-${subcategoryKey}-${chapterKey}`) ? (
                                        <FolderOpen className="h-3 w-3 mr-2 flex-shrink-0 mt-0.5" />
                                      ) : (
                                        <Folder className="h-3 w-3 mr-2 flex-shrink-0 mt-0.5" />
                                      )}
                                      <span className="break-all whitespace-normal leading-relaxed">{chapter.name}</span>
                                    </div>
                                  </Button>

                                  {expandedChapters.includes(`${vehicleKey}-${categoryKey}-${subcategoryKey}-${chapterKey}`) && (
                                    <div className="ml-4 space-y-1">
                                      {renderDocuments(chapter.documents, 4)}
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default DocumentTree;