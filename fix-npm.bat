@echo off
echo Cleaning npm cache and node_modules...

REM 刪除 node_modules
if exist node_modules (
    echo Removing node_modules...
    rmdir /s /q node_modules
)

REM 刪除 package-lock.json
if exist package-lock.json (
    echo Removing package-lock.json...
    del /f package-lock.json
)

REM 清理 npm 快取
echo Cleaning npm cache...
npm cache clean --force

REM 安裝套件
echo Installing packages...
npm install --legacy-peer-deps

echo Installation complete!
pause