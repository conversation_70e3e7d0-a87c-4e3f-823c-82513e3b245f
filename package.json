{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@headlessui/react": "^2.2.6", "@heroicons/react": "^2.2.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-slot": "^1.2.3", "@react-pdf/renderer": "^4.3.0", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-router-dom": "^5.3.3", "autoprefixer": "^10.4.21", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.532.0", "postcss": "^8.5.6", "react": "^19.1.1", "react-dom": "^19.1.1", "react-pdf": "^7.7.0", "react-router-dom": "^7.7.1", "react-scripts": "5.0.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "start:dev": "set REACT_APP_ENV=development && react-scripts start", "start:prod": "set REACT_APP_ENV=production && react-scripts start", "build:prod": "set REACT_APP_ENV=production && react-scripts build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@jridgewell/source-map": "^0.3.10", "cross-env": "^10.0.0"}}